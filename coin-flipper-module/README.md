# 🪙 CoinFlipper Module for Vue.js

โมดูลสำหรับการทอยเหรียญแบบ 3D ที่สามารถนำไปใช้กับ Vue.js ได้โดยตรง โดยรวม Three.js, AudioManager, และ CoinRenderer เข้าด้วยกันเป็นชุดเดียว

## ✨ Features

- 🎯 **Idle Animation**: เหรียญหมุนไปเรื่อยๆ ด้วยความเร็วที่กำหนดได้
- 🎲 **Toss Animation**: การแสดงเหรียญกำลังถูกทอยพร้อมเสียงและกำหนดผลลัพธ์ได้
- 🔊 **Sound Effects**: เสียงการทอย, เสียงชนะ, เสียงแพ้
- 🎨 **3D Graphics**: ใช้ Three.js สำหรับ animation ที่สมจริง
- 📱 **Responsive**: รองรับการใช้งานบนอุปกรณ์ต่างๆ
- 🔧 **TypeScript Support**: มี type definitions ครบถ้วน
- 🚀 **Easy Integration**: สามารถ import ใช้ใน Vue.js ได้ทันที
- 📦 **Self-contained**: รวม Three.js ไว้ในโมดูลแล้ว ไม่ต้องติดตั้งแยก

## 📁 Folder Structure

```
coin-flipper-module/
├── src/
│   ├── coin-flipper.js      # โมดูลหลัก (JavaScript)
│   └── coin-flipper.d.ts    # TypeScript definitions
├── examples/
│   ├── vue-example.vue      # ตัวอย่าง Vue.js (JavaScript)
│   └── vue-typescript-example.vue  # ตัวอย่าง Vue.js (TypeScript)
├── demo/
│   └── coin-flipper-demo.html  # Demo แบบ HTML ธรรมดา
├── docs/
│   └── COIN_FLIPPER_DOCS.md    # เอกสารการใช้งานแบบละเอียด
├── package.json
└── README.md               # เอกสารนี้
```

## 🚀 Quick Start

### 1. Copy Module
```bash
# คัดลอก folder coin-flipper-module ไปยังโปรเจคของคุณ
cp -r coin-flipper-module /path/to/your/project/
```

### 2. Basic Usage
```vue
<template>
  <div>
    <canvas ref="coinCanvas" width="400" height="400"></canvas>
    <button @click="flipCoin" :disabled="isFlipping">Flip Coin</button>
    <p>Result: {{ result }}</p>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CoinFlipper } from './coin-flipper-module/src/coin-flipper.js'

const coinCanvas = ref()
const coinFlipper = ref(null)
const isFlipping = ref(false)
const result = ref(null)

onMounted(async () => {
  coinFlipper.value = new CoinFlipper(coinCanvas.value)
  await coinFlipper.value.ready()
  await coinFlipper.value.startIdle()
})

onBeforeUnmount(() => {
  if (coinFlipper.value) {
    coinFlipper.value.destroy()
  }
})

const flipCoin = async () => {
  isFlipping.value = true
  result.value = await coinFlipper.value.toss()
  isFlipping.value = false
}
</script>
```

## 📚 API Reference

### CoinFlipper Class

```javascript
const coinFlipper = new CoinFlipper(canvasId, options)

// Methods
await coinFlipper.ready()           // รอให้พร้อมใช้งาน
await coinFlipper.startIdle()       // เริ่ม idle animation
await coinFlipper.stopIdle()        // หยุด idle animation
const result = await coinFlipper.toss()  // ทอยเหรียญ (สุ่ม)
const result = await coinFlipper.toss('heads')  // ทอยแบบกำหนดผล
await coinFlipper.playWinSound()    // เล่นเสียงชนะ
await coinFlipper.playLoseSound()   // เล่นเสียงแพ้
await coinFlipper.resize()          // ปรับขนาด
coinFlipper.destroy()               // ทำลายและทำความสะอาด

// Properties
coinFlipper.status  // สถานะปัจจุบัน
```

### Options
```javascript
{
  autoLoadThreeJS: true,     // โหลด Three.js อัตโนมัติ
  threeJSCDN: 'https://...',  // URL ของ Three.js CDN
  idleSpeed: 0.02,           // ความเร็วการหมุนแบบ idle
  flipDuration: 2000,        // ระยะเวลาการทอย (ms)
  enableSound: true          // เปิด/ปิดเสียง
}
```

## 🎯 Key Features

### 1. Idle Animation
เหรียญจะหมุนไปเรื่อยๆ ด้วยความเร็วที่กำหนดได้
```javascript
await coinFlipper.startIdle()  // เริ่มหมุน
await coinFlipper.stopIdle()   // หยุดหมุน
```

### 2. Toss Animation
การแสดงเหรียญกำลังถูกทอยพร้อมเสียง และกำหนดผลลัพธ์ของการทอยได้
```javascript
const result = await coinFlipper.toss()        // สุ่มผล
const result = await coinFlipper.toss('heads') // กำหนดผล
const result = await coinFlipper.toss('tails') // กำหนดผล
```

### 3. Sound Effects
```javascript
await coinFlipper.playWinSound()   // เสียงชนะ
await coinFlipper.playLoseSound()  // เสียงแพ้
// เสียงการทอยจะเล่นอัตโนมัติเมื่อเรียก toss()
```

## 🔧 TypeScript Support

```typescript
import { CoinFlipper, FlipResult, CoinFlipperOptions } from './coin-flipper-module/src/coin-flipper.js'

const options: CoinFlipperOptions = {
  idleSpeed: 0.02,
  flipDuration: 2000,
  enableSound: true
}

const coinFlipper = new CoinFlipper('canvas', options)
const result: FlipResult = await coinFlipper.toss()
```

## 🎮 Examples & Demo

### Examples
- `examples/vue-example.vue` - ตัวอย่าง Vue.js component (JavaScript)
- `examples/vue-typescript-example.vue` - ตัวอย่าง Vue.js component (TypeScript)

### Demo
- `demo/coin-flipper-demo.html` - Demo แบบ HTML ธรรมดาสำหรับทดสอบ

เปิดไฟล์ `demo/coin-flipper-demo.html` ในเบราว์เซอร์เพื่อทดสอบการทำงาน

## 📖 Documentation

ดูเอกสารการใช้งานแบบละเอียดใน [docs/COIN_FLIPPER_DOCS.md](docs/COIN_FLIPPER_DOCS.md)

## 📱 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🛠️ Installation

### วิธีที่ 1: Copy Module
```bash
# คัดลอก folder ทั้งหมด
cp -r coin-flipper-module /path/to/your/project/
```

### วิธีที่ 2: Individual Files
```bash
# คัดลอกเฉพาะไฟล์ที่ต้องการ
cp coin-flipper-module/src/coin-flipper.js /path/to/your/project/
cp coin-flipper-module/src/coin-flipper.d.ts /path/to/your/project/
```

## 🎯 Best Practices

1. **Memory Management**: เรียก `destroy()` เมื่อไม่ใช้งาน
2. **Error Handling**: ใช้ try-catch กับ async methods
3. **Performance**: ใช้ `requestAnimationFrame` สำหรับ status updates

## 📄 License

MIT License - สามารถใช้งานได้อย่างอิสระ

---

## 🚀 ความพิเศษของโมดูลนี้

✅ **ไม่ต้องติดตั้ง Three.js แยก** - รวมไว้ในโมดูลแล้ว  
✅ **ใช้งานง่าย** - เพียง import และเรียกใช้  
✅ **TypeScript Support** - มี type definitions ครบถ้วน  
✅ **Vue.js Ready** - ออกแบบมาสำหรับ Vue.js โดยเฉพาะ  
✅ **Customizable** - ปรับแต่งได้ตามต้องการ  
✅ **Performance Optimized** - จัดการ memory และ resources อย่างมีประสิทธิภาพ  
✅ **Complete Examples** - มีตัวอย่างและ demo ครบถ้วน
