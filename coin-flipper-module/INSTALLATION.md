# 🛠️ Installation Guide

คู่มือการติดตั้งและใช้งาน CoinFlipper Module สำหรับ Vue.js

## 📦 วิธีการติดตั้ง

### วิธีที่ 1: Copy <PERSON> (แนะนำ)

```bash
# คัดลอก folder coin-flipper-module ไปยังโปรเจคของคุณ
cp -r coin-flipper-module /path/to/your/vue-project/src/components/
```

### วิธีที่ 2: Copy เฉพาะไฟล์ที่จำเป็น

```bash
# สร้าง folder สำหรับโมดูล
mkdir -p /path/to/your/project/src/modules/coin-flipper

# คัดลอกไฟล์หลัก
cp coin-flipper-module/src/coin-flipper.js /path/to/your/project/src/modules/coin-flipper/
cp coin-flipper-module/src/coin-flipper.d.ts /path/to/your/project/src/modules/coin-flipper/
cp coin-flipper-module/index.js /path/to/your/project/src/modules/coin-flipper/
```

### วิธีที่ 3: Git Submodule (สำหรับ Git projects)

```bash
# เพิ่มเป็น submodule
git submodule add https://github.com/yourusername/coin-flipper-module.git src/modules/coin-flipper

# อัพเดท submodule
git submodule update --init --recursive
```

## 🚀 การใช้งานใน Vue.js Projects

### Vue 3 + Composition API

```vue
<template>
  <div class="coin-flipper-container">
    <canvas ref="coinCanvas" width="400" height="400"></canvas>
    <div class="controls">
      <button @click="startIdle" :disabled="status.isFlipping">Start Idle</button>
      <button @click="stopIdle" :disabled="status.isFlipping">Stop Idle</button>
      <button @click="flipCoin" :disabled="status.isFlipping">Flip Coin</button>
    </div>
    <p>Result: {{ result }}</p>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CoinFlipper } from '@/modules/coin-flipper'

const coinCanvas = ref()
const coinFlipper = ref(null)
const result = ref(null)
const status = ref({})

onMounted(async () => {
  coinFlipper.value = new CoinFlipper(coinCanvas.value, {
    idleSpeed: 0.02,
    flipDuration: 2000,
    enableSound: true
  })
  
  await coinFlipper.value.ready()
  await coinFlipper.value.startIdle()
  
  // อัพเดทสถานะ
  setInterval(() => {
    status.value = coinFlipper.value.status
  }, 100)
})

onBeforeUnmount(() => {
  if (coinFlipper.value) {
    coinFlipper.value.destroy()
  }
})

const startIdle = async () => {
  await coinFlipper.value.startIdle()
}

const stopIdle = async () => {
  await coinFlipper.value.stopIdle()
}

const flipCoin = async () => {
  result.value = await coinFlipper.value.toss()
}
</script>
```

### Vue 3 + Options API

```vue
<template>
  <div class="coin-flipper-container">
    <canvas ref="coinCanvas" width="400" height="400"></canvas>
    <button @click="flipCoin" :disabled="isFlipping">Flip Coin</button>
    <p>Result: {{ result }}</p>
  </div>
</template>

<script>
import { CoinFlipper } from '@/modules/coin-flipper'

export default {
  name: 'CoinFlipperComponent',
  data() {
    return {
      coinFlipper: null,
      result: null,
      isFlipping: false
    }
  },
  async mounted() {
    this.coinFlipper = new CoinFlipper(this.$refs.coinCanvas)
    await this.coinFlipper.ready()
    await this.coinFlipper.startIdle()
  },
  beforeUnmount() {
    if (this.coinFlipper) {
      this.coinFlipper.destroy()
    }
  },
  methods: {
    async flipCoin() {
      this.isFlipping = true
      try {
        this.result = await this.coinFlipper.toss()
      } finally {
        this.isFlipping = false
      }
    }
  }
}
</script>
```

### TypeScript Support

```typescript
// types/coin-flipper.d.ts (ถ้าใช้ TypeScript)
declare module '@/modules/coin-flipper' {
  export * from './coin-flipper-module/src/coin-flipper'
}
```

```vue
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CoinFlipper, type FlipResult, type CoinFlipperOptions } from '@/modules/coin-flipper'

const coinCanvas = ref<HTMLCanvasElement>()
const coinFlipper = ref<CoinFlipper | null>(null)
const result = ref<FlipResult | null>(null)

const options: CoinFlipperOptions = {
  idleSpeed: 0.02,
  flipDuration: 2000,
  enableSound: true
}

onMounted(async () => {
  if (coinCanvas.value) {
    coinFlipper.value = new CoinFlipper(coinCanvas.value, options)
    await coinFlipper.value.ready()
    await coinFlipper.value.startIdle()
  }
})

const flipCoin = async (): Promise<void> => {
  if (coinFlipper.value) {
    result.value = await coinFlipper.value.toss()
  }
}
</script>
```

## ⚙️ Configuration

### Vite Configuration

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/modules/coin-flipper': resolve(__dirname, 'src/modules/coin-flipper-module')
    }
  }
})
```

### Webpack Configuration

```javascript
// webpack.config.js
module.exports = {
  resolve: {
    alias: {
      '@/modules/coin-flipper': path.resolve(__dirname, 'src/modules/coin-flipper-module')
    }
  }
}
```

### Nuxt.js Configuration

```javascript
// nuxt.config.js
export default {
  alias: {
    '@/modules/coin-flipper': resolve(__dirname, 'modules/coin-flipper-module')
  }
}
```

## 🎨 CSS Styling

```css
.coin-flipper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.coin-flipper-container canvas {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.controls button {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.controls button:hover:not(:disabled) {
  background: #0056b3;
}

.controls button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
```

## 🐛 Troubleshooting

### Three.js ไม่โหลด
```javascript
// ตรวจสอบว่า Three.js โหลดแล้วหรือไม่
const coinFlipper = new CoinFlipper(canvas, {
  autoLoadThreeJS: true,  // เปิดการโหลดอัตโนมัติ
  threeJSCDN: 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js'
})
```

### เสียงไม่เล่น
```javascript
// เรียก resumeAudioContext หลังจาก user interaction
document.addEventListener('click', async () => {
  if (coinFlipper.value) {
    await coinFlipper.value.ready()
    // เสียงจะเล่นได้แล้ว
  }
}, { once: true })
```

### Canvas ไม่แสดงผล
```javascript
// ตรวจสอบว่า canvas element มีอยู่
onMounted(async () => {
  await nextTick() // รอให้ DOM render เสร็จ
  if (coinCanvas.value) {
    coinFlipper.value = new CoinFlipper(coinCanvas.value)
  }
})
```

### Memory Leaks
```javascript
// ใช้ destroy() เสมอใน beforeUnmount
onBeforeUnmount(() => {
  if (coinFlipper.value) {
    coinFlipper.value.destroy()
    coinFlipper.value = null
  }
})
```

## 📱 Mobile Support

```css
/* Responsive design */
@media (max-width: 768px) {
  .coin-flipper-container canvas {
    width: 300px;
    height: 300px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .controls button {
    width: 100%;
    margin: 5px 0;
  }
}
```

## 🔧 Advanced Usage

### Custom Options
```javascript
const coinFlipper = new CoinFlipper(canvas, {
  autoLoadThreeJS: false,  // ปิดการโหลด Three.js อัตโนมัติ
  idleSpeed: 0.05,         // หมุนเร็วขึ้น
  flipDuration: 3000,      // ทอยนานขึ้น
  enableSound: false       // ปิดเสียง
})
```

### Error Handling
```javascript
try {
  const result = await coinFlipper.toss()
  console.log('Result:', result)
} catch (error) {
  console.error('Flip failed:', error)
  // Handle error
}
```

## 📞 Support

หากมีปัญหาหรือข้อสงสัย:

1. ตรวจสอบ console สำหรับ error messages
2. ดู examples ใน `examples/` folder
3. ทดสอบด้วย `demo/coin-flipper-demo.html`
4. อ่านเอกสารใน `docs/COIN_FLIPPER_DOCS.md`
