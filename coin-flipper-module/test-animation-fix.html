<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        canvas {
            border: 2px solid #333;
            border-radius: 8px;
            background: #222;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            text-align: center;
            font-size: 18px;
            margin: 10px 0;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px auto;
            max-width: 600px;
        }
    </style>
</head>
<body>
    <h1>🪙 Animation Fix Test</h1>
    
    <div class="info">
        <h3>Testing Animation Continuity Fix</h3>
        <p>This test verifies that the finish scene animation starts from the exact same position where the flipping animation ended, without any visual jump or discontinuity.</p>
        <p><strong>What to look for:</strong> The coin should pause briefly at its final flip position before smoothly transitioning to the presentation view.</p>
    </div>
    
    <div class="status" id="status">Loading...</div>
    
    <canvas id="coinCanvas" width="400" height="400"></canvas>
    
    <div class="controls">
        <button onclick="flipCoin()">🎲 Flip Coin</button>
        <button onclick="startIdle()">▶️ Start Idle</button>
        <button onclick="stopIdle()">⏸️ Stop Idle</button>
    </div>

    <div id="result" class="status"></div>

    <!-- Load Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Load CoinFlipper -->
    <script src="src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        async function init() {
            try {
                updateStatus('Initializing CoinFlipper...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });
                
                updateStatus('CoinFlipper instance created ✓');
                
                // Wait for ready
                await coinFlipper.ready();
                updateStatus('CoinFlipper ready ✓');
                
                // Start idle animation
                await coinFlipper.startIdle();
                updateStatus('Ready! Coin is spinning ✓');
                
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                console.error('Initialization error:', error);
            }
        }
        
        async function startIdle() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.startIdle();
                updateStatus('Idle animation started');
            } catch (error) {
                updateStatus(`Error starting idle: ${error.message}`);
            }
        }
        
        async function stopIdle() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.stopIdle();
                updateStatus('Idle animation stopped');
            } catch (error) {
                updateStatus(`Error stopping idle: ${error.message}`);
            }
        }
        
        async function flipCoin() {
            if (!coinFlipper) return;
            
            try {
                updateStatus('Flipping coin... Watch for smooth transition!');
                document.getElementById('result').textContent = '';
                
                // Stop idle first
                await coinFlipper.stopIdle();
                
                // Flip the coin
                const result = await coinFlipper.toss();
                
                document.getElementById('result').textContent = `Result: ${result.toUpperCase()}`;
                updateStatus('Flip complete! Did you notice the smooth transition?');
                
                // Start idle again after a delay
                setTimeout(async () => {
                    await coinFlipper.startIdle();
                    updateStatus('Back to idle animation');
                }, 2000);
                
            } catch (error) {
                updateStatus(`Error flipping coin: ${error.message}`);
                console.error('Flip error:', error);
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
