/**
 * CoinFlipper Module for Vue.js
 * รวม Three.js, AudioManager, และ CoinRenderer เข้าด้วยกันเป็นชุดเดียว
 * สามารถ import ใช้ใน Vue.js ได้โดยตรง
 */

// Import Three.js (จะโหลดจาก CDN หรือ npm package)
let THREE;

// ตรวจสอบว่า Three.js มีอยู่แล้วหรือไม่
if (typeof window !== 'undefined' && window.THREE) {
    THREE = window.THREE;
} else if (typeof require !== 'undefined') {
    try {
        THREE = require('three');
    } catch (e) {
        console.warn('Three.js not found. Please install: npm install three');
    }
}

/**
 * AudioManager Class - จัดการเสียงทั้งหมด
 */
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API not supported');
        }
    }

    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    // เสียงการทอยเหรียญ (realistic coin toss sound)
    generateFlipSound() {
        if (!this.audioContext) return;

        const now = this.audioContext.currentTime;

        // 1. Metal friction (white noise + lowpass for scrape feel)
        this.playMetalScrape(now, 0.25);

        // 2. Coin bounce (more spaced out)
        const bounceTimes = [0.3, 0.7, 1.0, 1.2];
        bounceTimes.forEach((delay, i) => {
            this.playMetalClick(now + delay, 1 - i * 0.15);
        });

        // 3. Coin spin and slow stop (low frequency oscillator wobble)
        this.playWobbleSpin(now + 0.9, 0.6);
    }

    playMetalScrape(startTime, duration) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            data[i] = (Math.random() * 2 - 1) * (1 - i / bufferSize); // white noise fade
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(800, startTime);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(startTime);
        noise.stop(startTime + duration);
    }

    playMetalClick(time, volume = 1) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * 0.03;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / bufferSize, 2);
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'highpass';
        filter.frequency.setValueAtTime(1500, time);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.2 * volume, time);
        gain.gain.exponentialRampToValueAtTime(0.001, time + 0.1);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(time);
        noise.stop(time + 0.1);
    }

    playWobbleSpin(startTime, duration) {
        if (!this.audioContext) return;

        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();

        osc.type = 'sine';
        osc.frequency.setValueAtTime(30, startTime);
        osc.frequency.exponentialRampToValueAtTime(8, startTime + duration); // slow wobble

        gain.gain.setValueAtTime(0.08, startTime);
        gain.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

        osc.connect(gain).connect(this.audioContext.destination);
        osc.start(startTime);
        osc.stop(startTime + duration);
    }

    // เสียงชนะ (ascending chime)
    generateWinSound() {
        if (!this.audioContext) return;

        const frequencies = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
        
        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime + index * 0.15);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime + index * 0.15);
            gainNode.gain.linearRampToValueAtTime(0.2, this.audioContext.currentTime + index * 0.15 + 0.05);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + index * 0.15 + 0.4);
            
            oscillator.start(this.audioContext.currentTime + index * 0.15);
            oscillator.stop(this.audioContext.currentTime + index * 0.15 + 0.4);
        });
    }

    // เสียงแพ้ (descending tone)
    generateLoseSound() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.5);
        oscillator.type = 'sawtooth';
        
        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }
}

/**
 * CoinRenderer Class - จัดการ 3D animation ด้วย Three.js
 */
class CoinRenderer {
    constructor(canvasId, options = {}) {
        if (!THREE) {
            throw new Error('Three.js is required but not found. Please include Three.js before using CoinFlipper.');
        }

        this.canvas = typeof canvasId === 'string' ? document.getElementById(canvasId) : canvasId;
        this.options = {
            idleSpeed: 0.02,
            flipDuration: 2000,
            ...options
        };
        
        this.isFlipping = false;
        this.isIdle = false;
        this.isShowingFinishScene = false;
        this.animationId = null;

        // Animation state
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.gravity = -0.015;
        this.bounceCount = 0;
        this.maxBounces = 3;
        this.bounceDamping = 0.6;
        this.rotationDamping = 0.98;

        // Finish scene animation state
        this.finishSceneProgress = 0;
        this.finishSceneDuration = 2000; // 2 seconds
        this.finishSceneStartTime = 0;
        this.finishScenePauseTime = 500; // 0.5 second pause at final flip position
        this.finishScenePhase = 'pause'; // 'pause' or 'animate'
        
        this.initThreeJS();
        this.createCoin();
        this.setupLighting();
        this.startRenderLoop();
    }

    initThreeJS() {
        console.log('🎬 Initializing Three.js...');
        console.log('Canvas element:', this.canvas);

        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Get canvas dimensions
        const width = this.canvas.width || this.canvas.clientWidth || 400;
        const height = this.canvas.height || this.canvas.clientHeight || 400;
        console.log('Canvas dimensions:', width, 'x', height);

        this.camera = new THREE.PerspectiveCamera(
            75,
            width / height,
            0.1,
            1000
        );
        this.camera.position.set(0, 0, 3);
        console.log('Camera position:', this.camera.position);

        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(width, height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        console.log('✅ Three.js initialized');
    }

    createCoin() {
        console.log('🪙 Creating coin...');
        const geometry = new THREE.CylinderGeometry(0.8, 0.8, 0.1, 32);

        // Create textures for heads and tails
        const headsTexture = this.createHeadsTexture();
        const tailsTexture = this.createTailsTexture();
        const edgeTexture = this.createEdgeTexture();

        // Materials array for different faces
        const materials = [
            new THREE.MeshPhongMaterial({ map: edgeTexture }), // Side
            new THREE.MeshPhongMaterial({ map: headsTexture }), // Top (Heads)
            new THREE.MeshPhongMaterial({ map: tailsTexture })  // Bottom (Tails)
        ];

        this.coin = new THREE.Mesh(geometry, materials);
        this.coin.castShadow = true;
        this.coin.receiveShadow = true;

        // Set initial position
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        this.scene.add(this.coin);

        console.log('✅ Coin created and added to scene');
        console.log('Coin position:', this.coin.position);
        console.log('Scene children count:', this.scene.children.length);
    }

    createHeadsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Gold gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the H.jpeg image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load H.jpeg image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#8B4513';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('หัว', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        //HEADS : Set the image source
        img.src = 'images/h_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createTailsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Silver gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the coin face image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load T.png image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#2C2C2C';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('ก้อย', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        // TAILS: Set the image sources
        img.src = 'images/t_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createEdgeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');

        // Gold edge color
        const gradient = ctx.createLinearGradient(0, 0, 0, 32);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.5, '#FFA500');
        gradient.addColorStop(1, '#FFD700');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 32);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light (main light)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(2, 4, 2);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        this.scene.add(directionalLight);

        // Point light for extra shine
        const pointLight = new THREE.PointLight(0xffd700, 0.3, 10);
        pointLight.position.set(-2, 2, 2);
        this.scene.add(pointLight);
    }

    startRenderLoop() {
        console.log('✅ Starting render loop');
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);
            this.updateAnimation();
            this.renderer.render(this.scene, this.camera);
        };
        animate();
    }

    updateAnimation() {
        if (this.isIdle && !this.isFlipping && !this.isShowingFinishScene) {
            // Idle animation - หมุนเหรียญเบาๆ
            this.coin.rotation.x += this.options.idleSpeed;
        } else if (this.isFlipping) {
            // Flip animation logic จะถูกจัดการใน flipCoin method
            this.updateFlipAnimation();
        } else if (this.isShowingFinishScene) {
            this.updateFinishScene();
        }
    }

    updateFlipAnimation() {
        // อัพเดท physics
        this.velocity.y += this.gravity;
        this.coinPositionY += this.velocity.y;
        
        // อัพเดท rotation
        this.coinRotationX += this.angularVelocity.x;
        this.coinRotationY += this.angularVelocity.y;
        this.coinRotationZ += this.angularVelocity.z;
        
        // Apply damping
        this.angularVelocity.x *= this.rotationDamping;
        this.angularVelocity.y *= this.rotationDamping;
        this.angularVelocity.z *= this.rotationDamping;
        
        // Handle bouncing
        if (this.coinPositionY <= -1.5 && this.velocity.y < 0) {
            this.coinPositionY = -1.5;
            this.velocity.y = -this.velocity.y * this.bounceDamping;
            this.bounceCount++;

            // Add some random rotation on bounce
            this.angularVelocity.x += (Math.random() - 0.5) * 0.1;
            this.angularVelocity.z += (Math.random() - 0.5) * 0.1;

            // Stop bouncing after max bounces and low velocity
            if (this.bounceCount >= this.maxBounces && Math.abs(this.velocity.y) < 0.05) {
                this.velocity.y = 0;
                this.coinPositionY = -1.5;
                this.stopFlipping();
            }
        }
        
        // Apply transformations
        this.coin.position.y = this.coinPositionY;
        this.coin.rotation.x = this.coinRotationX;
        this.coin.rotation.y = this.coinRotationY;
        this.coin.rotation.z = this.coinRotationZ;
    }

    // เริ่ม idle animation
    startIdle() {
        console.log('✅ Starting idle animation');

        // Reset เหรียญให้ตั้งขึ้นก่อนเริ่มหมุน
        this.resetCoinToVertical();

        this.isIdle = true;
        this.isFlipping = false;
    }

    // Reset เหรียญให้อยู่ในตำแหน่งตั้งขึ้น (vertical position)
    resetCoinToVertical() {
        // Reset camera to original position
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // Reset coin to vertical position (standing on its edge)
        this.coin.scale.set(1, 1, 1);
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(Math.PI / 2, 0, 0); // Rotate 90 degrees around X-axis to stand vertically

        // Reset internal state to match the vertical position
        this.coinRotationX = Math.PI / 2; // 90 degrees in radians
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.bounceCount = 0;

        // Reset ambient light intensity
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }

        console.log('🔄 Coin reset to vertical position for idle animation');
    }

    // หยุด idle animation
    stopIdle() {
        this.isIdle = false;
    }

    // ทอยเหรียญ
    async flipCoin(result = null) {
        if (this.isFlipping || this.isShowingFinishScene) return;

        this.isFlipping = true;
        this.isIdle = false;
        this.isShowingFinishScene = false;
        this.bounceCount = 0;

        // Store the intended result for use in finish scene
        this.intendedResult = typeof result === 'string' ? result.toLowerCase() : result;

        // Reset camera to original position for flip animation
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // Reset coin scale to original size for flip animation
        this.coin.scale.set(1, 1, 1);

        // Reset position and rotation for new flip
        this.coinPositionY = 0;
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;

        // Set initial velocities for realistic flip
        this.velocity.y = 0.3; // Upward velocity

        // Calculate target rotation based on result (using X-axis for natural end-over-end flip)
        const baseRotations = 4; // Number of full rotations
        const targetRotationX = result === 'heads'
            ? baseRotations * Math.PI * 2
            : (baseRotations * Math.PI * 2) + Math.PI;

        // Set angular velocities
        this.angularVelocity.x = targetRotationX / 120; // Spread over ~2 seconds
        this.angularVelocity.y = (Math.random() - 0.5) * 0.1; // Random side rotation
        this.angularVelocity.z = (Math.random() - 0.5) * 0.1; // Random wobble

        // Return promise that resolves when animation completes
        return new Promise((resolve) => {
            this.flipResolve = resolve;
        });
    }

    stopFlipping() {
        console.log('🔄 Stopping flip animation at position:', {
            x: this.coinRotationX,
            y: this.coinRotationY,
            z: this.coinRotationZ,
            posY: this.coinPositionY
        });
        this.isFlipping = false;
        // Start the finish scene animation
        this.startFinishScene();
    }

    startFinishScene() {
        console.log('🎬 Starting finish scene with pause phase');
        this.isShowingFinishScene = true;
        this.finishSceneStartTime = Date.now();
        this.finishSceneProgress = 0;
        this.finishScenePhase = 'pause'; // Start with pause phase

        // Use the intended result stored during flipCoin
        const finalResult = this.intendedResult || this.getCurrentResult();

        // Store the exact current state as the final flip position (no changes during pause)
        this.finishSceneFinalFlipRotation = {
            x: this.coinRotationX,
            y: this.coinRotationY,
            z: this.coinRotationZ
        };
        this.finishSceneFinalFlipPosition = this.coinPositionY;

        // Calculate target rotation to show the winning face clearly in vertical orientation
        const targetX = finalResult === 'heads' ? 0 : Math.PI;

        // Normalize current X rotation to find shortest path
        const currentX = ((this.coinRotationX % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);
        let deltaX = targetX - currentX;

        // Choose shortest rotation path
        if (deltaX > Math.PI) {
            deltaX -= Math.PI * 2;
        } else if (deltaX < -Math.PI) {
            deltaX += Math.PI * 2;
        }

        // Set target rotation for vertical orientation
        this.finishSceneTargetRotation = {
            x: currentX + deltaX,
            y: 0,
            z: 0
        };

        // Store target position (elevated and centered for clear visibility)
        this.finishSceneTargetPosition = -0.3;

        // Store camera animation data
        this.finishSceneStartCameraPosition = this.camera.position.clone();
        this.finishSceneTargetCameraPosition = new THREE.Vector3(0, 1.5, 2.5);

        // Store coin scale animation data
        this.finishSceneStartScale = this.coin.scale.clone();
        this.finishSceneTargetScale = new THREE.Vector3(1.2, 1.2, 1.2);
    }

    updateFinishScene() {
        const elapsed = Date.now() - this.finishSceneStartTime;

        if (this.finishScenePhase === 'pause') {
            // During pause phase, maintain exact final flip position
            this.coinRotationX = this.finishSceneFinalFlipRotation.x;
            this.coinRotationY = this.finishSceneFinalFlipRotation.y;
            this.coinRotationZ = this.finishSceneFinalFlipRotation.z;
            this.coinPositionY = this.finishSceneFinalFlipPosition;

            // Apply transformations to coin (no changes during pause)
            this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);
            this.coin.position.y = this.coinPositionY;

            // Check if pause time has elapsed
            if (elapsed >= this.finishScenePauseTime) {
                console.log('🎬 Switching to animate phase after pause');
                // Switch to animate phase
                this.finishScenePhase = 'animate';
                this.finishSceneAnimateStartTime = Date.now();

                // Set animation start values to current final flip position
                this.finishSceneStartRotation = {
                    x: this.finishSceneFinalFlipRotation.x,
                    y: this.finishSceneFinalFlipRotation.y,
                    z: this.finishSceneFinalFlipRotation.z
                };
                this.finishSceneStartPosition = this.finishSceneFinalFlipPosition;
            }
            return;
        }

        // Animate phase - smooth transition to final presentation
        const animateElapsed = Date.now() - this.finishSceneAnimateStartTime;
        this.finishSceneProgress = Math.min(animateElapsed / this.finishSceneDuration, 1);

        // Use easing function for smooth animation
        const easeProgress = this.easeInOutCubic(this.finishSceneProgress);

        // Interpolate rotation
        this.coinRotationX = this.lerp(
            this.finishSceneStartRotation.x,
            this.finishSceneTargetRotation.x,
            easeProgress
        );
        this.coinRotationY = this.lerp(
            this.finishSceneStartRotation.y,
            this.finishSceneTargetRotation.y,
            easeProgress
        );
        this.coinRotationZ = this.lerp(
            this.finishSceneStartRotation.z,
            this.finishSceneTargetRotation.z,
            easeProgress
        );

        // Interpolate position
        this.coinPositionY = this.lerp(
            this.finishSceneStartPosition,
            this.finishSceneTargetPosition,
            easeProgress
        );

        // Interpolate camera position for optimal vertical viewing
        this.camera.position.lerpVectors(
            this.finishSceneStartCameraPosition,
            this.finishSceneTargetCameraPosition,
            easeProgress * 0.6
        );

        // Ensure camera looks down at the coin for clear result visibility
        this.camera.lookAt(0, this.coinPositionY, 0);

        // Interpolate coin scale for emphasis
        this.coin.scale.lerpVectors(
            this.finishSceneStartScale,
            this.finishSceneTargetScale,
            easeProgress
        );

        // Apply transformations to coin
        this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);
        this.coin.position.y = this.coinPositionY;

        // Add subtle floating effect in the final position
        if (this.finishSceneProgress >= 0.8) {
            const totalElapsed = Date.now() - this.finishSceneStartTime;
            const floatOffset = Math.sin(totalElapsed * 0.003) * 0.02;
            this.coin.position.y += floatOffset;

            // Add subtle glow effect by adjusting ambient light
            const glowIntensity = 0.6 + Math.sin(totalElapsed * 0.005) * 0.1;
            if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
                this.scene.children[0].intensity = glowIntensity;
            }
        }

        // End finish scene animation
        if (this.finishSceneProgress >= 1) {
            this.isShowingFinishScene = false;

            // Reset ambient light intensity to normal
            if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
                this.scene.children[0].intensity = 0.6;
            }

            // Ensure coin maintains its final vertical position and rotation
            this.coin.rotation.set(
                this.finishSceneTargetRotation.x,
                this.finishSceneTargetRotation.y,
                this.finishSceneTargetRotation.z
            );
            this.coin.position.y = this.finishSceneTargetPosition;
            this.coin.scale.copy(this.finishSceneTargetScale);

            // Update internal rotation tracking to match final state
            this.coinRotationX = this.finishSceneTargetRotation.x;
            this.coinRotationY = this.finishSceneTargetRotation.y;
            this.coinRotationZ = this.finishSceneTargetRotation.z;
            this.coinPositionY = this.finishSceneTargetPosition;

            // Resolve the flip promise now that the finish scene is complete
            if (this.flipResolve) {
                this.flipResolve(this.intendedResult || this.getCurrentResult());
                this.flipResolve = null;
            }
        }
    }

    // Easing function for smooth animation
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // Linear interpolation helper
    lerp(start, end, t) {
        return start + (end - start) * t;
    }

    getCurrentResult() {
        // Determine if coin is showing heads or tails based on X rotation
        const normalizedRotation = ((this.coinRotationX % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);
        return normalizedRotation < Math.PI ? 'heads' : 'tails';
    }

    // ทำลาย renderer
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
    }

    // Method to reset coin to initial state (useful for game reset)
    resetCoin() {
        this.isFlipping = false;
        this.isShowingFinishScene = false;
        this.isIdle = false;

        // Reset camera to original position
        this.camera.position.set(0, 0, 3);
        this.camera.lookAt(0, 0, 0);

        // Reset coin to original state
        this.coin.scale.set(1, 1, 1);
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        // Reset internal state
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.bounceCount = 0;

        // Reset ambient light intensity
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }
    }

    // Pulse effect for coin interaction
    pulseEffect() {
        if (this.isFlipping || this.isShowingFinishScene) return;

        const originalScale = this.coin.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(1.1);

        // Animate scale up and down
        const duration = 300;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            let scale;
            if (progress < 0.5) {
                // Scale up
                const t = progress * 2;
                scale = originalScale.clone().lerp(targetScale, t);
            } else {
                // Scale down
                const t = (progress - 0.5) * 2;
                scale = targetScale.clone().lerp(originalScale, t);
            }

            this.coin.scale.copy(scale);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.coin.scale.copy(originalScale);
            }
        };

        animate();
    }

    // Resize handler
    resize() {
        if (this.camera && this.renderer) {
            this.camera.aspect = this.canvas.clientWidth / this.canvas.clientHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        }
    }
}

/**
 * CoinFlipper Main Class - คลาสหลักสำหรับใช้งานใน Vue.js
 */
class CoinFlipper {
    constructor(canvasId, options = {}) {
        this.options = {
            autoLoadThreeJS: true,
            threeJSCDN: 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js',
            idleSpeed: 0.02,
            flipDuration: 2000,
            enableSound: true,
            ...options
        };

        this.canvasId = canvasId;
        this.audioManager = null;
        this.coinRenderer = null;
        this.isInitialized = false;
        this.initPromise = null;

        // Auto-initialize if Three.js is available
        if (THREE || !this.options.autoLoadThreeJS) {
            this.init();
        } else {
            this.initPromise = this.loadThreeJS().then(() => this.init());
        }
    }

    // โหลด Three.js จาก CDN
    async loadThreeJS() {
        if (typeof window === 'undefined') return;

        return new Promise((resolve, reject) => {
            if (window.THREE) {
                THREE = window.THREE;
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = this.options.threeJSCDN;
            script.onload = () => {
                THREE = window.THREE;
                resolve();
            };
            script.onerror = () => reject(new Error('Failed to load Three.js'));
            document.head.appendChild(script);
        });
    }

    // เริ่มต้นระบบ
    async init() {
        if (this.isInitialized) return;

        try {
            // Initialize audio manager
            if (this.options.enableSound) {
                this.audioManager = new AudioManager();
            }

            // Initialize coin renderer
            this.coinRenderer = new CoinRenderer(this.canvasId, {
                idleSpeed: this.options.idleSpeed,
                flipDuration: this.options.flipDuration
            });

            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize CoinFlipper:', error);
            throw error;
        }
    }

    // รอให้ระบบพร้อมใช้งาน
    async ready() {
        if (this.initPromise) {
            await this.initPromise;
        }
        if (!this.isInitialized) {
            await this.init();
        }
    }

    // เริ่ม idle animation (เหรียญหมุนเบาๆ)
    async startIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.startIdle();
        }
    }

    // หยุด idle animation
    async stopIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.stopIdle();
        }
    }

    // ทอยเหรียญ
    async toss(result = null, playSound = true) {
        await this.ready();

        if (!this.coinRenderer) {
            throw new Error('CoinRenderer not initialized');
        }

        // เล่นเสียงการทอย
        if (playSound && this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateFlipSound();
        }

        // ทำการทอย
        const finalResult = await this.coinRenderer.flipCoin(result);

        return finalResult;
    }

    // เล่นเสียงชนะ
    async playWinSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateWinSound();
        }
    }

    // เล่นเสียงแพ้
    async playLoseSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateLoseSound();
        }
    }

    // รีเซ็ตเหรียญกลับสู่สถานะเริ่มต้น
    async resetCoin() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resetCoin();
        }
    }

    // เอฟเฟกต์ pulse เมื่อคลิกเหรียญ
    async pulseEffect() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.pulseEffect();
        }
    }

    // ปรับขนาดตาม canvas
    async resize() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resize();
        }
    }

    // ทำลายทุกอย่าง
    destroy() {
        if (this.coinRenderer) {
            this.coinRenderer.destroy();
            this.coinRenderer = null;
        }
        this.audioManager = null;
        this.isInitialized = false;
    }

    // ตรวจสอบสถานะ
    get status() {
        return {
            isInitialized: this.isInitialized,
            hasAudio: !!this.audioManager,
            hasRenderer: !!this.coinRenderer,
            isFlipping: this.coinRenderer ? this.coinRenderer.isFlipping : false,
            isIdle: this.coinRenderer ? this.coinRenderer.isIdle : false,
            isShowingFinishScene: this.coinRenderer ? this.coinRenderer.isShowingFinishScene : false
        };
    }
}

// Export สำหรับใช้ใน different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js/CommonJS
    module.exports = { CoinFlipper, AudioManager, CoinRenderer };
} else if (typeof define === 'function' && define.amd) {
    // AMD
    define([], function() {
        return { CoinFlipper, AudioManager, CoinRenderer };
    });
} else {
    // Browser global
    window.CoinFlipper = CoinFlipper;
    window.CoinFlipperAudioManager = AudioManager;
    window.CoinFlipperRenderer = CoinRenderer;
}
