{"name": "coin-flipper-vue-module", "version": "1.0.0", "description": "3D Coin Flipper Module for Vue.js with Three.js integration - Self-contained module with idle and toss animations", "main": "src/coin-flipper.js", "types": "src/coin-flipper.d.ts", "files": ["src/", "examples/", "docs/", "demo/", "README.md"], "scripts": {"demo": "open demo/coin-flipper-demo.html", "test": "echo \"Open demo/coin-flipper-demo.html in browser to test\" && exit 0"}, "keywords": ["coin-flipper", "vue", "v<PERSON><PERSON><PERSON>", "three.js", "3d", "animation", "game", "casino", "flip", "coin", "typescript", "idle", "toss", "sound-effects"], "author": "CoinFlipper <PERSON>", "license": "MIT", "peerDependencies": {"vue": "^3.0.0"}, "optionalDependencies": {"three": "^0.128.0"}, "devDependencies": {"@types/three": "^0.128.0", "typescript": "^4.9.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/coin-flipper-vue-module.git"}, "bugs": {"url": "https://github.com/yourusername/coin-flipper-vue-module/issues"}, "homepage": "https://github.com/yourusername/coin-flipper-vue-module#readme", "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}