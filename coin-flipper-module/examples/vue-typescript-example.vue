<template>
  <div class="coin-flipper-demo">
    <h2>🪙 Coin Flipper Demo (TypeScript)</h2>
    
    <div class="coin-container">
      <canvas 
        ref="coinCanvas" 
        width="400" 
        height="400"
        class="coin-canvas"
      ></canvas>
    </div>

    <div class="status-info">
      <p><strong>Status:</strong> {{ statusText }}</p>
      <p><strong>Last Result:</strong> {{ lastResult || 'None' }}</p>
      <p><strong>Balance:</strong> ${{ balance }}</p>
    </div>

    <div class="controls">
      <div class="idle-controls">
        <button 
          @click="startIdle" 
          :disabled="status.isFlipping || status.isIdle"
          class="btn btn-secondary"
        >
          🔄 Start Idle
        </button>
        <button 
          @click="stopIdle" 
          :disabled="status.isFlipping || !status.isIdle"
          class="btn btn-secondary"
        >
          ⏹️ Stop Idle
        </button>
      </div>

      <div class="betting-controls">
        <div class="bet-amount">
          <label>Bet Amount: $</label>
          <input 
            v-model.number="betAmount" 
            type="number" 
            min="1" 
            :max="balance"
            :disabled="status.isFlipping"
          >
        </div>
        
        <div class="bet-choice">
          <label>Choose:</label>
          <select v-model="betChoice" :disabled="status.isFlipping">
            <option value="heads">👑 Heads</option>
            <option value="tails">🦅 Tails</option>
          </select>
        </div>

        <button 
          @click="flipCoin" 
          :disabled="status.isFlipping || betAmount > balance || betAmount < 1"
          class="btn btn-primary"
        >
          {{ status.isFlipping ? '🎲 Flipping...' : '🎲 Flip Coin!' }}
        </button>
      </div>

      <div class="test-controls">
        <h4>Test Controls:</h4>
        <button @click="testHeads" :disabled="status.isFlipping" class="btn btn-test">
          👑 Test Heads
        </button>
        <button @click="testTails" :disabled="status.isFlipping" class="btn btn-test">
          🦅 Test Tails
        </button>
        <button @click="testRandom" :disabled="status.isFlipping" class="btn btn-test">
          🎲 Test Random
        </button>
      </div>
    </div>

    <div class="history" v-if="gameHistory.length > 0">
      <h4>Game History:</h4>
      <div class="history-list">
        <div 
          v-for="(game, index) in gameHistory.slice(-5)" 
          :key="index"
          class="history-item"
          :class="{ win: game.won, lose: !game.won }"
        >
          <span>{{ game.choice }} vs {{ game.result }}</span>
          <span>{{ game.won ? '+' : '-' }}${{ game.amount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { CoinFlipper, type CoinFlipperStatus, type FlipResult } from '../src/coin-flipper.js'

// Types
interface GameRecord {
  choice: FlipResult
  result: FlipResult
  amount: number
  won: boolean
  timestamp: Date
}

// Reactive data
const coinCanvas = ref<HTMLCanvasElement>()
const coinFlipper = ref<CoinFlipper | null>(null)
const status = ref<CoinFlipperStatus>({
  isInitialized: false,
  hasAudio: false,
  hasRenderer: false,
  isFlipping: false,
  isIdle: false
})

const lastResult = ref<FlipResult | null>(null)
const balance = ref<number>(1000)
const betAmount = ref<number>(50)
const betChoice = ref<FlipResult>('heads')
const gameHistory = ref<GameRecord[]>([])

let statusInterval: number | null = null

// Computed
const statusText = computed(() => {
  if (!status.value.isInitialized) return 'Initializing...'
  if (status.value.isFlipping) return 'Flipping coin...'
  if (status.value.isIdle) return 'Idle (spinning)'
  return 'Ready'
})

// Methods
const updateStatus = (): void => {
  if (coinFlipper.value) {
    status.value = { ...coinFlipper.value.status }
  }
}

const startIdle = async (): Promise<void> => {
  try {
    if (coinFlipper.value) {
      await coinFlipper.value.startIdle()
      updateStatus()
    }
  } catch (error) {
    console.error('Failed to start idle:', error)
  }
}

const stopIdle = async (): Promise<void> => {
  try {
    if (coinFlipper.value) {
      await coinFlipper.value.stopIdle()
      updateStatus()
    }
  } catch (error) {
    console.error('Failed to stop idle:', error)
  }
}

const flipCoin = async (): Promise<void> => {
  if (betAmount.value > balance.value || betAmount.value < 1 || !coinFlipper.value) return

  try {
    // หยุด idle ก่อนทอย
    await coinFlipper.value.stopIdle()
    
    // ทอยเหรียญ
    const result = await coinFlipper.value.toss()
    lastResult.value = result
    
    // ตรวจสอบผลลัพธ์
    const won = result === betChoice.value
    
    // อัพเดทเงิน
    if (won) {
      balance.value += betAmount.value
      await coinFlipper.value.playWinSound()
    } else {
      balance.value -= betAmount.value
      await coinFlipper.value.playLoseSound()
    }

    // บันทึกประวัติ
    gameHistory.value.push({
      choice: betChoice.value,
      result: result,
      amount: betAmount.value,
      won: won,
      timestamp: new Date()
    })

    // กลับไป idle หลังจากทอยเสร็จ
    setTimeout(async () => {
      if (coinFlipper.value) {
        await coinFlipper.value.startIdle()
        updateStatus()
      }
    }, 1000)

  } catch (error) {
    console.error('Failed to flip coin:', error)
  }
}

const testHeads = async (): Promise<void> => {
  try {
    if (coinFlipper.value) {
      await coinFlipper.value.stopIdle()
      const result = await coinFlipper.value.toss('heads')
      lastResult.value = result
      setTimeout(async () => {
        if (coinFlipper.value) {
          await coinFlipper.value.startIdle()
        }
      }, 1000)
    }
  } catch (error) {
    console.error('Test failed:', error)
  }
}

const testTails = async (): Promise<void> => {
  try {
    if (coinFlipper.value) {
      await coinFlipper.value.stopIdle()
      const result = await coinFlipper.value.toss('tails')
      lastResult.value = result
      setTimeout(async () => {
        if (coinFlipper.value) {
          await coinFlipper.value.startIdle()
        }
      }, 1000)
    }
  } catch (error) {
    console.error('Test failed:', error)
  }
}

const testRandom = async (): Promise<void> => {
  try {
    if (coinFlipper.value) {
      await coinFlipper.value.stopIdle()
      const result = await coinFlipper.value.toss()
      lastResult.value = result
      setTimeout(async () => {
        if (coinFlipper.value) {
          await coinFlipper.value.startIdle()
        }
      }, 1000)
    }
  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Lifecycle
onMounted(async () => {
  try {
    if (!coinCanvas.value) {
      throw new Error('Canvas element not found')
    }

    // สร้าง CoinFlipper instance
    coinFlipper.value = new CoinFlipper(coinCanvas.value, {
      idleSpeed: 0.02,
      flipDuration: 2000,
      enableSound: true
    })

    // รอให้พร้อมใช้งาน
    await coinFlipper.value.ready()
    
    // อัพเดทสถานะ
    updateStatus()
    
    // เริ่ม idle animation
    await coinFlipper.value.startIdle()
    updateStatus()

    // ตั้ง interval สำหรับอัพเดทสถานะ
    statusInterval = window.setInterval(() => {
      updateStatus()
    }, 100)

  } catch (error) {
    console.error('Failed to initialize CoinFlipper:', error)
    alert('Failed to initialize CoinFlipper. Please check console for details.')
  }
})

onBeforeUnmount(() => {
  // ทำความสะอาด
  if (statusInterval) {
    clearInterval(statusInterval)
  }
  if (coinFlipper.value) {
    coinFlipper.value.destroy()
  }
})
</script>

<style scoped>
/* Same styles as the JavaScript version */
.coin-flipper-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.coin-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.coin-canvas {
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.status-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #007bff;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.idle-controls, .betting-controls, .test-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.betting-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin: 5px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-test {
  background: #28a745;
  color: white;
}

.history {
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin: 5px 0;
}

.history-item.win {
  background: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.history-item.lose {
  background: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}
</style>
